// Support Ticket specific styles using Recipe pattern
.support-ticket-section-wrapper {
  .tickets-list-wrapper {
    padding-top: var(--spacing-xxl);
    .tickets-details-container & {
      height: auto;
    }
  }
  .vertical-divider {
    margin-right: var(--spacing-lg);
  }

  .no-tickets-left {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }

  .show-tickets-wrap {
    position: relative;
    height: calc(100vh - 130px - var(--banner-height));
    display: flex;
    flex-direction: column;
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    border: none;

    .support-ticket-container {
      padding: var(--spacing-none) var(--spacing-none) var(--spacing-none)
        var(--spacing-xxl) !important;
    }

    .section-right-content {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none)
        var(--spacing-none);
      height: calc(100% - 49px);
      overflow: auto;
      flex: 1;

      // Remove overflow when tickets-details-container is present to avoid double scroll
      &:has(.tickets-details-container) {
        overflow: visible;
        height: auto;

        // On mobile, restore scroll to parent container
        @media (max-width: 1199px) {
          overflow: auto;
          height: calc(100vh - 200px);
        }
      }

      .no-data-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
      }
    }

    // Support ticket header styling (replaces recipe-category-filter-wrap)
    .ticket-container {
      display: flex;
      transition: margin-left 0.3s ease;
      width: 100%;
      gap: var(--spacing-lg);

      .tickets {
        width: 100%;
        max-width: 300px;

        @media (max-width: 1199px) {
          display: none;
        }
      }

      .menu-icon-wrap {
        display: none;

        @media (max-width: 1199px) {
          display: block;
        }
      }

      .drawer-toggle-icon {
        cursor: pointer;
      }

      .tickets-details {
        position: relative;
        z-index: 1;
        width: 75%;
        min-height: 400px;

        // When no ticket is selected, center the no-data view
        &:has(.no-data-container) {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        // Fallback for browsers that don't support :has()
        .no-data-container {
          width: 100%;
          height: 100%;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          min-height: 400px;
        }

        @media (max-width: 1199px) {
          width: 66%;
        }

        @media (max-width: 1199px) {
          width: 93%;
        }

        @media (max-width: 767px) {
          width: 91%;
        }

        @media (max-width: 575px) {
          width: 87%;
        }
      }

      @media (max-width: 374px) {
        gap: var(--spacing-sm);
      }
    }

    .support-ticket-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      background: transparent;
      box-shadow: none;
      border-radius: 0;
      border: none;
    }

    // Ticket details content - Basic styling for all ticket details
    .ticket-details-content {
      display: flex;
      flex-direction: column;
      gap: 0;
    }

    // Drawer wrapper
    .drawer-wrap {
      position: relative;
    }
  }
}

// Global styles (not nested under support-ticket-section-wrapper)
.ticket-filter-drawer {
  z-index: 1300 !important;
}

.sorting-popover {
  .MuiPaper-root {
    z-index: 99999 !important;
    width: auto !important;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .sort-option {
    .options {
      cursor: pointer;
      padding: var(--spacing-sm) var(--spacing-none);
      text-align: left;
    }
  }
}

.tickets-details-container {
  display: flex;
  align-items: flex-start;
  height: calc(100vh - 160px); // More precise calculation
  overflow: hidden;

  // Ensure proper height on mobile devices
  @media (max-width: 1199px) {
    height: auto; // Remove fixed height
    min-height: auto; // Let content determine height
    overflow: visible; // Remove overflow restriction
  }

  .tickets-list-wrapper {
    width: 20%;
    flex-shrink: 0;
    // Add scroll functionality for tickets list
    max-height: calc(100vh - 180px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: var(--spacing-lg);
  }

  .ticket-details-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    // Add proper scroll to right side content
    max-height: calc(100vh - 180px);
    overflow-y: auto;
    overflow-x: hidden;
    // Add padding to create space between content and scrollbar
    padding-right: var(--spacing-xxl);
    padding-top: var(--spacing-xxl);
    @media(max-width: 575px) {
      padding-right: var(--spacing-lg);
    }
  }

  @media (max-width: 1199px) {
    flex-direction: column;
    max-height: none; // Remove height restriction on mobile

    .tickets-list-wrapper {
      width: 100%;
      max-width: 100%;
      max-height: none; // Remove scroll - let page handle it
      overflow: visible; // Remove individual scroll
      margin-bottom: var(--spacing-lg);
    }

    .ticket-details-content {
      width: 100%;
      max-width: 100%;
      max-height: none; // Remove scroll - let page handle it
      overflow: visible; // Remove individual scroll
      min-height: auto; // Let content determine height
    }

    // Hide desktop-only elements on mobile
    .desktop-only-divider,
    .desktop-only-details {
      display: none !important;
    }
  }
}

// Mobile detail view styles
.mobile-detail-view {
  width: 100%;

  .mobile-detail-header {
    margin-bottom: var(--spacing-lg);

    .mobile-back-button {
      font-size: var(--icon-size-sm);
      color: var(--icon-color-primary);

      &:hover {
        color: var(--icon-color-primary);
        opacity: 0.8;
      }
    }
  }

  // Show mobile detail view only on screens <= 1199px
  @media (min-width: 1200px) {
    display: none;
  }
}
