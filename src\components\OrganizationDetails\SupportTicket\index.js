'use client';
import { Box, Divider } from '@mui/material';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import Ticket from './Ticket';
import TicketDetails from './TicketDetails';
import NoTickets from './NoTickets';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ContentLoader from '@/components/UI/ContentLoader';
import { supportTicketService } from '@/services/supportTicketService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './supportticket.scss';

export default function SupportTicket({
  selectedTicketId,
  ticketsData = null, // Accept tickets data as props
  onTicketsUpdate = null, // Callback to update tickets in parent
}) {
  const router = useRouter();
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [tickets, setTickets] = useState([]);
  const [loadingTickets, setLoadingTickets] = useState(true); // Loading state for tickets list
  const [initialPageLoad, setInitialPageLoad] = useState(true); // Main page loading state
  const [showMobileDetailView, setShowMobileDetailView] = useState(false); // Mobile detail view state

  // Function to fetch tickets from API
  const fetchTickets = async (filters = {}, isInitialLoad = false) => {
    try {
      if (!isInitialLoad) {
        setLoadingTickets(true); // Start loading for subsequent calls
      }

      // Build filter object for API call
      const filterParams = {};
      if (filters.status) {
        filterParams.ticket_status = filters.status;
      }

      const response = await supportTicketService.getTicketsList(
        '', // search
        1, // page
        50, // limit - increased to show more tickets
        filterParams, // filters
        null // sort
      );
      if (response && response.tickets && Array.isArray(response.tickets)) {
        setTickets(response.tickets);
      } else {
        setTickets([]);
      }
    } catch (error) {
      console.error('Error fetching tickets:', error);
      setApiMessage('error', error?.response?.data?.message);
      setTickets([]);
    } finally {
      if (!isInitialLoad) {
        setLoadingTickets(false); // Stop loading for subsequent calls
      } else {
        // For initial load, stop both loaders
        setLoadingTickets(false);
        setInitialPageLoad(false);
      }
    }
  };

  // Initialize tickets from props or fetch from API
  useEffect(() => {
    if (ticketsData && Array.isArray(ticketsData) && ticketsData.length > 0) {
      // Use tickets data directly without mapping
      setTickets(ticketsData);
      setLoadingTickets(false); // Stop loading when data is provided via props
      setInitialPageLoad(false); // Stop initial page load
    } else {
      // If no props provided, fetch tickets from API
      fetchTickets({}, true); // Pass true for initial load
    }
  }, [ticketsData]);

  // Effect to set selected ticket when selectedTicketId is provided
  useEffect(() => {
    if (selectedTicketId && !selectedTicket) {
      // If we have tickets loaded, try to find the ticket
      if (tickets?.length > 0) {
        const ticketToSelect = tickets?.find(
          (t) => t?.id === parseInt(selectedTicketId)
        );
        if (ticketToSelect) {
          // Call API to get detailed ticket information
          fetchTicketDetails(ticketToSelect.id);
        } else {
          // Ticket not found in list, fetch directly
          fetchTicketDetails(parseInt(selectedTicketId));
        }
      } else if (ticketsData?.length > 0) {
        // If tickets are not loaded yet but we have ticketsData, try to find from there
        const ticketToSelect = ticketsData?.find(
          (t) => t?.id === parseInt(selectedTicketId)
        );
        if (ticketToSelect) {
          // Call API to get detailed ticket information
          fetchTicketDetails(ticketToSelect.id);
        } else {
          // Ticket not found in data, fetch directly
          fetchTicketDetails(parseInt(selectedTicketId));
        }
      } else {
        // If no tickets data available, directly fetch the ticket details
        fetchTicketDetails(parseInt(selectedTicketId));
      }
    }
  }, [selectedTicketId, tickets, ticketsData, selectedTicket]);

  // Handle window resize to manage mobile view
  useEffect(() => {
    const handleResize = () => {
      // If screen becomes larger than 1199px, hide mobile detail view
      if (window.innerWidth > 1199 && showMobileDetailView) {
        setShowMobileDetailView(false);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [showMobileDetailView]);

  // Function to fetch ticket details from API
  const fetchTicketDetails = async (ticketId) => {
    try {
      const ticketDetails =
        await supportTicketService.getTicketDetails(ticketId);
      if (ticketDetails) {
        // Use ticket details directly without mapping
        setSelectedTicket(ticketDetails);
      } else {
        setApiMessage('error', 'Failed to fetch ticket details');
      }
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleTicketClick = (ticket) => {
    // Call API to get detailed ticket information
    if (ticket?.id) {
      // Update URL to include ticket ID for persistence on refresh
      const currentUrl = new URL(window.location);
      currentUrl.searchParams.set('id', ticket.id);
      router.push(currentUrl.pathname + currentUrl.search, { scroll: false });

      fetchTicketDetails(ticket?.id);

      // Check if screen width is <= 1199px and show mobile detail view
      if (window.innerWidth <= 1199) {
        setShowMobileDetailView(true);
      }
    } else {
      // Fallback to using the ticket data from props if no ID
      setSelectedTicket(ticket);

      // Check if screen width is <= 1199px and show mobile detail view
      if (window.innerWidth <= 1199) {
        setShowMobileDetailView(true);
      }
    }
  };

  // Handle back button click for mobile detail view
  const handleMobileBackClick = () => {
    setShowMobileDetailView(false);
    setSelectedTicket(null);

    // Remove ticket ID from URL
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.delete('id');
    router.push(currentUrl.pathname + currentUrl.search, { scroll: false });
  };

  const handleTicketStatusChange = (ticketId, newStatus) => {
    // Update the specific ticket's status in the tickets array
    const updatedTickets = tickets?.map((ticket) =>
      ticket?.id === ticketId ? { ...ticket, ticket_status: newStatus } : ticket
    );

    setTickets(updatedTickets);

    // Also update selectedTicket if it's the same ticket
    if (selectedTicket?.id === ticketId) {
      setSelectedTicket((prev) => ({ ...prev, ticket_status: newStatus }));
    }

    // Notify parent component if callback provided
    if (onTicketsUpdate) {
      onTicketsUpdate(updatedTickets);
    }
  };

  const handleTicketDelete = (deletedTicket) => {
    // Remove the deleted ticket from the tickets array
    const updatedTickets = tickets?.filter(
      (ticket) => ticket?.id !== deletedTicket?.id
    );
    setTickets(updatedTickets);

    // Clear selectedTicket if it was the deleted ticket
    if (selectedTicket?.id === deletedTicket?.id) {
      setSelectedTicket(null);
    }

    // Notify parent component if callback provided
    if (onTicketsUpdate) {
      onTicketsUpdate(updatedTickets);
    }
  };

  const handleCreateTicket = () => {
    router.push('/support-ticket/create-ticket');
  };

  // Back navigation handlers - following staff module pattern
  const handleBackNavigation = () => {
    // Check if there's saved filter data from AllTicketsList
    const redirectData = fetchFromStorage(identifiers?.RedirectData);

    if (redirectData && redirectData?.IsFromUser) {
      // Clear the redirect data since we're going back
      removeFromStorage(identifiers?.RedirectData);
      // Navigate back to all tickets list (filters will be restored automatically)
      router.push('/support-ticket/all-tickets');
    } else {
      // No saved data, navigate normally
      router.push('/support-ticket/all-tickets');
    }
  };

  // Render no tickets view if no tickets exist
  const renderNoTicketsView = () => (
    <Box className="section-wrapper support-ticket-section-wrapper">
      {/* Only Right Section - Create Ticket or Empty State */}
      <Box className="section-right">
        <Box className="show-tickets-wrap">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title">
              <p className="sub-header-text d-flex align-center gap-sm">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={handleBackNavigation}
                />
                Support Ticket
              </p>
            </Box>
          </Box>
          <Divider />
          {/* Show CreateTicket form if isCreateMode is true, else show NoTickets */}
          <Box className="section-right-content">
            <NoTickets onAddTicketClick={handleCreateTicket} />
          </Box>
        </Box>
      </Box>
    </Box>
  );

  // Render main tickets view with everything in right section
  const renderTicketsView = () => (
    <Box className="section-wrapper support-ticket-section-wrapper">
      {/* Right Section - Everything */}
      <Box className="section-right">
        <Box className="show-tickets-wrap">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title">
              <p className="sub-header-text d-flex align-center">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={handleBackNavigation}
                />
                Support Tickets
              </p>
            </Box>
          </Box>
          <Divider />

          {/* Right Section Content */}
          <Box className="section-right-content support-ticket-container">
            {/* Mobile Detail View - Show only when showMobileDetailView is true */}
            {showMobileDetailView && selectedTicket ? (
              <Box className="mobile-detail-view">
                <Box className="mobile-detail-header">
                  <Box className="d-flex align-center gap-5 mb16">
                    <ArrowBackIosIcon
                      className="cursor-pointer mobile-back-button"
                      onClick={handleMobileBackClick}
                    />
                    <p className="sub-header-text">Ticket Details</p>
                  </Box>
                </Box>
                <TicketDetails ticket={selectedTicket} />
              </Box>
            ) : (
              /* Desktop View and Mobile List View */
              <Box className="d-flex align-start tickets-details-container">
                {/* Tickets List */}
                <Box className="tickets-list-wrapper">
                  <Ticket
                    onTicketClick={handleTicketClick}
                    ticketsList={tickets || []} // Always pass an array, even if empty
                    selectedTicket={selectedTicket}
                    hideStatusChip={true}
                    showBottomPadding={true}
                    onTicketStatusChange={handleTicketStatusChange}
                    onTicketDelete={handleTicketDelete}
                    onStatusFilterChange={(status) => {
                      // Clear selected ticket when filtering to avoid showing old details
                      setSelectedTicket(null);

                      // API call for status filter to private/tickets/list
                      const filters = {};
                      if (status) {
                        filters.status = status;
                      }

                      // Call fetchTickets with filters
                      fetchTickets(filters);
                    }}
                    hideStatusFilter={false} // Show status filter
                    isLoading={loadingTickets} // Pass loading state
                  />
                </Box>
                <Divider
                  orientation="vertical"
                  flexItem
                  className="vertical-divider desktop-only-divider"
                />
                {/* Ticket Details - Hidden on mobile */}
                <Box className="ticket-details-content desktop-only-details">
                  {selectedTicket ? (
                    <TicketDetails ticket={selectedTicket} />
                  ) : null}
                </Box>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );

  // Show single page loader during initial load
  if (initialPageLoad) {
    return (
      <Box className="section-wrapper">
        <Box className="section-right">
          <Box
            className="section-right-content d-flex align-center justify-center"
            style={{ minHeight: '60vh' }}
          >
            <ContentLoader />
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <>
      {/* Show tickets view if tickets exist, otherwise show no tickets view */}
      {(tickets && tickets.length > 0) ||
      (ticketsData && Array.isArray(ticketsData) && ticketsData.length > 0) ||
      selectedTicketId
        ? renderTicketsView()
        : renderNoTicketsView()}
    </>
  );
}
