@import '@/styles/variable.scss';

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  // overflow-x: hidden; // Commented for scroll issue
  padding: 0;
}

.hide-overflow {
  overflow: hidden !important;
}

body {
  overflow: hidden;
  /* color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb)); */
  .success {
    border: 1px solid $color-Dark-10;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: $color-Success-Background;
    color: $color-Success;
    text-transform: capitalize;
  }

  .failed {
    border: 1px solid var(----color-danger);
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: $color-Alert-Background;
    color: $color-Alert !important;
    text-transform: capitalize;
  }



  .ongoing {
    border: 1px solid $color-Primary-100;
    border-radius: 8px;
    padding: 1.5px 8px;
    background-color: var(--color-white);
    color: $color-Primary-100;
    text-transform: capitalize;
  }
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-default {
  cursor: default !important;
}

.cursor-auto {
  cursor: auto !important;
}

.cursor-notallow {
  cursor: not-allowed !important;
}

.cursor-text {
  cursor: text !important;
}

.h1 {
  font-family: $regulerFont !important;
  font-size: 48px !important;
  line-height: 58px !important;
}

.h2 {
  font-family: $regulerFont !important;
  font-size: 40px !important;
  line-height: 48px !important;
  font-weight: 700 !important;
}

.h3 {
  font-family: $regulerFont !important;
  font-size: 32px !important;
  line-height: 38px !important;
}

.h5 {
  font-family: $regulerFont !important;
  font-size: 20px !important;
  line-height: 24px !important;
  font-weight: 700 !important;
}

.p20 {
  font-family: $PrimaryFont !important;
  font-size: 20px !important;
  line-height: 32px !important;
  letter-spacing: -0.5px !important;
}

.p16 {
  font-family: $PrimaryFont !important;
  font-size: 16px !important;
  line-height: 24px !important;
  letter-spacing: -0.5px !important;
}

.p14 {
  font-family: $PrimaryFont !important;
  font-size: 14px !important;
  line-height: 21px !important;
  letter-spacing: -0.5px !important;
}

.p12 {
  font-family: $PrimaryFont !important;
  font-size: 12px !important;
  line-height: 18px !important;
  letter-spacing: -0.5px !important;
}

.Inter12 {
  font-family: Inter, sans-serif !important;
  font-size: 12px !important;
  line-height: 18px !important;
  letter-spacing: -0.5px !important;
  color: #33343a !important;
  font-weight: 600 !important;
}

.label {
  font-family: $PrimaryFont !important;
  font-size: 10px !important;
  line-height: 16px !important;
  letter-spacing: -0.5px !important;
}

.text-underline {
  text-decoration: underline !important;
  text-underline-offset: 4px !important;
}

.fw400 {
  font-weight: 400 !important;
}

.fw500 {
  font-weight: 500 !important;
}

.fw600 {
  font-weight: 600 !important;
}

.fw800 {
  font-weight: 800 !important;
}

.fw700 {
  font-weight: 700 !important;
}

.pt0 {
  padding-top: 0px !important;
}
.p0 {
  padding: 0px !important;
}
.pr0 {
  padding-right: 0px !important;
}

.pb0 {
  padding-bottom: 0px !important;
}

.pl0 {
  padding-left: 0px !important;
}

.pt4 {
  padding-top: 4px !important;
}

.pr4 {
  padding-right: 4px !important;
}

.pb4 {
  padding-bottom: 4px !important;
}

.pl4 {
  padding-left: 4px !important;
}
.ml4 {
  margin-left: 4px !important;
}
.ml8 {
  margin-left: 8px !important;
}
.pt8 {
  padding-top: 8px !important;
}

.pr8 {
  padding-right: 8px !important;
}

.pb8 {
  padding-bottom: 8px !important;
}

.pl8 {
  padding-left: 8px !important;
}

.pt16 {
  padding-top: 16px !important;
}

.pt12 {
  padding-top: 12px !important;
}

.pr16 {
  padding-right: 16px !important;
}

.pb16 {
  padding-bottom: 16px !important;
}

.pl16 {
  padding-left: 16px !important;
}

.pt24 {
  padding-top: 24px !important;
}

.pt20 {
  padding-top: 20px !important;
}

.pr24 {
  padding-right: 24px !important;
}

.pb24 {
  padding-bottom: 24px !important;
}

.pl24 {
  padding-left: 24px !important;
}

.pt32 {
  padding-top: 32px !important;
}

.pr32 {
  padding-right: 32px !important;
}

.pb32 {
  padding-bottom: 32px !important;
}

.pl32 {
  padding-left: 32px !important;
}

.pt40 {
  padding-top: 40px !important;
}

.pr40 {
  padding-right: 40px !important;
}

.pb40 {
  padding-bottom: 40px !important;
}

.pl40 {
  padding-left: 40px !important;
}

.pt64 {
  padding-top: 64px !important;
}

.pr64 {
  padding-right: 64px !important;
}

.pb64 {
  padding-bottom: 64px !important;
}

.pl64 {
  padding-left: 64px !important;
}

.pt72 {
  padding-top: 72px !important;
}

.pr72 {
  padding-right: 72px !important;
}

.pb72 {
  padding-bottom: 72px !important;
}

.pl72 {
  padding-left: 72px !important;
}

.pt-56 {
  padding-top: 56px !important;
}

.mt0 {
  margin-top: 0px !important;
}

.mt3 {
  margin-top: 3px !important;
}

.mt4 {
  margin-top: 4px !important;
}

.pb56 {
  padding-bottom: 56px !important;
}

.mb16 {
  margin-bottom: 16px !important;
}

.mb32 {
  margin-bottom: 32px !important;
}

.mb24 {
  margin-bottom: 24px !important;
}

.mb0 {
  margin-bottom: 0px !important;
}

.mb2 {
  margin-bottom: 2px !important;
}

.mt32 {
  margin-top: 32px !important;
}

.mt8 {
  margin-top: 8px !important;
}

.mb8 {
  margin-bottom: 8px !important;
}

.mr8 {
  margin-right: 8px !important;
}
.mr16 {
  margin-right: 16px !important;
}

.mr20 {
  margin-right: 20px !important;
}

.mb20 {
  margin-right: 20px;
}

.ml0 {
  margin-left: 0px !important;
}

.ml16 {
  margin-left: 16px !important;
}
.ml24 {
  margin-left: 24px !important;
}

.ml30 {
  margin-left: 30px !important;
}

.mt16 {
  margin-top: 16px !important;
}

.mt24 {
  margin-top: 24px !important;
}

.w100 {
  width: 100% !important;
}

.mw100 {
  max-width: 100% !important;
}

.h100 {
  height: 100% !important;
}

.text-align {
  text-align: center !important;
}

.text-align-end {
  text-align: end !important;
}

.d-flex {
  display: flex !important;
}

.d-none {
  display: none !important;
}

.align-center {
  align-items: center !important;
}

.align-start {
  align-items: flex-start !important;
}

.align-end {
  align-items: flex-end !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-space-between {
  justify-content: space-between !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.text-capital {
  text-transform: capitalize !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-row {
  flex-direction: row;
}

.width45 {
  width: 45%;
}

.width30 {
  width: 30%;
}

.disabled-field {
  opacity: 0.6;
}
.Toastify {
  .Toastify__toast-container {
    width: max-content;
    // width: 450px;
    min-width: 350px;
    max-width: 450px;

    @media (max-width: 899px) {
      max-width: calc(100% - 64px);
    }
  }

  .Toastify__toast--success {
    background: $color-green !important;
  }

  .Toastify__toast-body div {
    font-family: var(--font-family-primary) !important;
    font-size: 14px !important;
    line-height: 21px !important;
    // letter-spacing: -0.5px !important;
  }

  .Toastify__toast-icon {
    svg {
      fill: var(--icon-color-white) !important;
    }
  }

  .Toastify__close-button {
    // align-self: center;
    // margin-top: 2px;
    svg {
      fill: var(--icon-color-white) !important;
    }
  }
}

.loader-wraper {
  position: fixed;
  top: 0;
  left: 0;
  background: transparent;
  width: 100%;
  height: 100%;
  z-index: 999;

  .loader-box {
    position: fixed;
    top: 50%;
    left: 50%;
  }

  .loader-icon {
    color: var(--color-primary);
  }
}

.table-container {
  margin-top: 24px;
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  padding: 20px 32px 32px;

  @media (max-width: 899px) {
    padding: 16px 12px 20px;
  }

  .MuiDataGrid-root {
    border-radius: var(--border-radius-lg);
    border: none;
    padding-bottom: 12px;

    .MuiDataGrid-withBorderColor {
      border: none;
    }

    .MuiDataGrid-columnHeaders {
      min-height: 48px !important;
      max-height: 48px !important;
      line-height: 48px !important;

      @media (max-width: 899px) {
        min-height: 40px !important;
        max-height: 40px !important;
        line-height: 40px !important;
      }

      .MuiDataGrid-columnSeparator {
        display: none;
      }

      &:hover {
        .MuiDataGrid-columnSeparator {
          display: none;
        }
      }

      .MuiDataGrid-columnHeader {
        height: 48px !important;

        @media (max-width: 899px) {
          height: 40px !important;
        }

        font-weight: 600;
        color: var(--text-color-slate-gray);

        .MuiDataGrid-iconButtonContainer {
          visibility: visible;
          width: auto;

          .MuiButtonBase-root {
            background-color: transparent;

            &:hover {
              svg {
                path {
                  stroke: var(--icon-color-primary); // $color-Primary-100;
                }
              }
            }
          }
        }

        &:focus {
          outline: none;
        }

        &:focus-within {
          outline: none;
        }
      }
    }

    .MuiDataGrid-row {
      &:hover {
        background-color: transparent;
      }
    }

    @media (max-width: 899px) {
      // .MuiDataGrid-columnHeader {
      //   height: 40px !important;
      //   min-height: 40px !important;
      // }

      // .MuiDataGrid-cell {
      //   height: 40px !important;
      //   line-height: 38px !important;
      // }

      // .MuiDataGrid-row {
      //   height: 40px !important;
      //   min-height: 40px !important;
      // }
    }

    .MuiDataGrid-cell:focus {
      outline: none;
    }

    .MuiDataGrid-cell:focus-within {
      outline: none;
    }

    .Mui-selected {
      background-color: transparent;
    }
  }

  .MuiDataGrid-sortIcon {
    opacity: 1 !important;
    color: $color-Dark-80;
  }

  .MuiDataGrid-colCellDivider {
    display: none !important;
  }

  .MuiDataGrid-menuIcon {
    display: none !important;
  }

  // .MuiDataGrid-scrollbar {
  //   display: none !important;
  // }
  .MuiDataGrid-scrollbar--vertical {
    display: none !important;
  }

  .MuiDataGrid-footerContainer {
    display: none !important;
  }
}

.text-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}

.text-ellipsis-line {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}
.text-ellipsis-line2 {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

.pagination-select {
  max-width: none;
  width: auto;
  margin-top: 5px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  border: 1px solid $color-Primary-10;

  .MuiButtonBase-root {
    padding: 2.5px 11px;
    font-family: $PrimaryFont;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: -0.5px;
    font-weight: 600;
    color: $color-Dark-80;

    &:hover {
      background-color: $color-primary !important; // $color-Primary-100;
      color: $color-White;

      svg {
        path {
          stroke: $color-White;
        }
      }
    }
  }

  .Mui-selected {
    background-color: $color-primary !important; // $color-Primary-100;
    color: $color-White;
  }
}

.close-icon-box {
  display: none;

  @media (max-width: 899px) {
    display: block;
    text-align: end;
    margin-bottom: 15px;
  }
}

.page-section {
  background-color: $color-White;
  padding: 56px 40px;
  margin-top: 24px;
  border-radius: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);

  @media (max-width: 899px) {
    padding: 36px 30px;
  }

  @media (max-width: 599px) {
    padding: 26px 20px;
  }

  @media (max-width: 399px) {
    padding: 26px 10px;
  }
}

.download-sec {
  border: 4px solid $color-dark-red;
  width: 100px;
  height: 110px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    width: 30px;
    height: 30px;
    fill: $color-dark-red !important;
  }

  .download-text {
    color: $color-dark-red;
  }
}

.upload-sec {
  border: 2px dashed var(--border-color-primary);
  width: 100px;
  height: 110px;
  border-radius: var(--border-radius-lg);
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    width: 30px;
    height: 30px;
    fill: var(--icon-color-primary) !important;
  }

  .upload-text {
    color: var(--text-color-slate-gray);
    padding: 0px 10px;
    .blue-text {
      color: var(--text-color-primary);
    }
  }
}

.color-red {
  color: var(--color-danger) !important;
}

.color-green {
  color: var(--color-green) !important;
}

.main-heading {
  color: $color-primary;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background-color: $color-White;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: $color-primary;
  border-radius: 3px;
}

.select-box {
  .textfeild-error {
    .MuiOutlinedInput-root:hover {
      fieldset {
        border: 1px solid $error !important;
      }
    }

    fieldset {
      border: 1px solid $error !important;
      border-color: $error !important;
      border-width: 1px !important;
    }

    label {
      color: $error !important;
    }

    svg {
      fill: $error !important;
    }
  }

  // .field-error {
  //   margin-top: 7px !important;
  // }
}

.leave-policy-input {
  .MuiInputBase-root {
    min-height: 32px !important;
    border-radius: 4px !important;
    .MuiInputBase-input {
      font-size: 14px;
      padding: 3px 16px !important;
      &::placeholder {
        font-size: 14px;
      }
    }
  }
}

.leave-policy-select-wrap {
  .MuiSelect-select {
    font-size: 14px;
    padding: 6px 7px 3px 15px;
    margin-top: 0px !important;
  }

  fieldset {
    height: 32px !important;
    border-radius: 8px;
    margin-top: 5px !important;
    border-radius: 4px !important;
  }

  .MuiSvgIcon-root {
    margin-top: 3px;
  }

  .placeholder {
    margin-top: 2px !important;
    font-family: Inter, sans-serif !important;
    font-size: 14px !important;
    font-weight: 300 !important;
  }
}

.date-picker-textfield {
  .textfeild-error {
    .MuiOutlinedInput-root:hover {
      fieldset {
        border: 1px solid $error !important;
        border-color: $error !important;
      }
    }

    fieldset {
      border: 1px solid $error !important;
      border-color: $error !important;
    }

    .MuiInputBase-root .MuiOutlinedInput-notchedOutline {
      border: 1px solid $error !important;
      border-color: $error !important;
    }

    label {
      color: $error !important;
    }

    svg {
      fill: $error !important;
    }
  }
}

.uploaded-media-sec {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
  svg path {
    fill: var(--icon-color-primary) !important;
  }

  .image-sec {
    display: flex;
    align-items: center;

    svg {
      margin-right: var(--spacing-sm);
      fill: var(--icon-color-primary) !important;
    }
  }
}

.link-text {
  cursor: pointer !important;
  text-decoration: underline !important;
  text-underline-offset: 2px;
  color: var(--color-primary) !important;
}

.primary-link-text {
  cursor: pointer !important;
  text-decoration: underline !important;
  text-underline-offset: 2px;
  color: var(--text-color-primary) !important;
}

.max-content {
  max-width: max-content !important;
}

.active-role {
  color: var(--color-green) !important;
}

.color-yellow {
  color: #ffd700;
}

.color-orange {
  color: var(--color-orange) !important;
}
.color-warning {
  color: var(--color-warning) !important;
}
.color-blue {
  color: $color-Primary-100 !important;
}

.color-dark-blue {
  color: $color-Dark-70 !important;
}

.color-primary {
  color: $color-primary;
}

.color-gray {
  color: $color-Dark-40 !important;
}

.custom-menu-list {
  width: 215px;
  max-width: 100%;
  padding: 6px 12px;
  margin-top: 14px;

  @media (max-width: 899px) {
    margin-top: 19px;
  }

  .folder-desc-divider {
    border-bottom: 1px solid $color-Primary-10;
    margin: 10px 0 !important;
  }

  .active-role {
    // background-color: $color-Success-Background;
    color: var(--color-green);
  }

  .role {
    display: flex;
    // max-width: max-content;
  }

  .profile-roles-section {
    .switch-roles {
      .switching {
        display: flex;
        align-items: center;

        .switch-text {
          padding-left: 12px;
          // color: $color-primary;
        }

        svg {
          // fill: $color-primary !important;
          width: 24px;
          height: 24px;
        }
      }

      .role-list {
        // padding-left: 36px;
        .role {
          width: 100%;
          margin-left: -5px;
          padding-left: 8px;
          &:hover {
            background: var(--color-primary-opacity);
            border-radius: var(--border-radius-lg);
          }
        }
      }
    }
  }

  .profile-logout {
    span {
      padding-left: 12px;
    }

    svg {
      width: 24px !important;
      height: 24px !important;
    }

    .icon-tabler-door-exit {
      width: 24px !important;
      height: 23px !important;
    }
  }

  .logout-text {
    svg {
      fill: $color-Alert !important;
    }

    span {
      color: var(----color-danger);
    }
  }
}

.password-textfield {
  .MuiInputBase-root {
    padding-right: 0 !important;
  }

  .eye-icon {
    position: absolute;
    right: 0;
  }
}

.password-error {
  .eye-icon svg {
    fill: $error;
  }
}

.active .icon-tabler-door-exit {
  path {
    fill: $color-primary !important;
  }
}

.overflow-hidden-dialogbox {
  overflow: hidden;

  .MuiDialog-container,
  .MuiPaper-root {
    overflow: hidden;
    overflow-y: hidden !important;
  }
}

.signature-section {
  width: 183px;
  border: 1px solid $color-primary;
  height: 63px;
  text-align: center;

  .signature-image {
    width: 180px;
  }
}

.content-loader {
  text-align: center;

  .loader {
    color: var(--color-primary);
    height: 30px !important;
    width: 30px !important;
  }
}

.upload-area {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.visiblity-hidden {
  visibility: hidden !important;
}

.custom-rechart-wrap {
  .recharts-wrapper {
    width: 100% !important;

    @media (max-width: 1499px) {
      // overflow-x: auto;
      max-height: 420px;
    }

    @media (max-width: 1299px) {
      // overflow-x: auto;
      max-height: 420px;
    }

    @media (max-width: 799px) {
      max-height: 360px;
    }

    @media (max-width: 599px) {
      // overflow-x: auto;
      max-height: 320px;
    }
  }

  .recharts-surface {
    // .yAxis {
    //   line {
    //     display: none;
    //   }
    // }

    // .xAxis {
    //   line {
    //     stroke: #d4d6d8;
    //   }
    // }

    .recharts-cartesian-axis-ticks {
      .recharts-text {
        font-size: 12px;
        line-height: 15px;
        font-weight: 400;
        font-family: $regulerFont;
        letter-spacing: 0px;
        color: $color-Black;
      }
    }
  }

  .recharts-legend-wrapper {
    bottom: -10px !important;
    width: 100% !important;

    ul {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      column-gap: 44px;
      list-style: none;
      margin: 0px;
      row-gap: 21px;

      li {
        font-size: 14px;
        line-height: 16px;
        font-weight: 600;
        font-family: $regulerFont;
        color: $color-Black;
        display: flex;
        align-items: center;
        column-gap: 6px;

        .legend-block {
          width: 17px;
          height: 17px;
          // border: 1px solid;
          // height: 2px;
        }
      }

      @media (max-width: 599px) {
        li {
          font-size: 7px;
          line-height: 10px;
          font-weight: 600;
          font-family: $regulerFont;
          color: $color-Black;
          display: flex;
          align-items: center;
          column-gap: 6px;

          .legend-block {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }

  .recharts-line-dots {
    circle {
      display: none;
    }
  }

  // .recharts-active-dot {
  //   circle {
  //     display: none;
  //   }
  // }
}

.custom-tooltip-chart {
  background: #ffffff;
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);

  .chart-label {
    font-size: 16px;
    font-family: $regulerFont;
    font-weight: 500;
  }

  .chart-value {
    font-size: 14px;
    font-family: $regulerFont;
    font-weight: 600;
  }
}

.color-picker-box {
  width: 100%;
  display: flex;
  padding: 12px;
  border-radius: 8px;
  background-color: $color-White;
  border: 1px solid $color-Dark-10;

  .color-box {
    width: 20px;
    height: 20px;
    background-color: $color-primary;
  }

  .color-name {
    line-height: 21px;
    font-weight: 400;
    font-size: 16px;
    font-family: $regulerFont;
    color: $color-Black;
    padding-left: 15px;
  }
}

.chromePicker-parent-sec {
  position: relative;

  .chromePicker-fore {
    position: absolute;
    bottom: 0px;
    z-index: 9999999;
  }
}

.branch-bg {
  box-shadow: $dashboard-box-bg;
}

.staff-bg {
  box-shadow: $dashboard-box-bg;
}

.playlist-bg {
  box-shadow: $dashboard-box-bg;
}

.dsr-bg {
  box-shadow: $dashboard-box-bg;
}

.list-branch-color {
  color: $list-branch-color;
}

.action-icon {
  background: $action-bg-color;
  padding: 5px;
  border-radius: 4px;

  .border-svg {
    fill: $color-Black !important;
  }

  svg {
    fill: $color-White !important;
    width: 18px !important;
    height: 18px !important;

    path {
      stroke: $color-Black;
    }

    g {
      path {
        stroke: none;
      }
    }
  }

  &:hover {
    background: $color-Dark-10;
  }
}

.list-user-icon {
  .MuiAvatar-root {
    height: 35px;
    width: 35px;
  }
}

.list-verified-icon {
  height: 15px !important;
  width: 15px !important;
  fill: $color-green !important;
}

.bg-amount-transparent {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  width: auto;
  word-break: break-word;

  span {
    background-color: lighten($list-amount-color, 65%);
    color: $list-amount-color;
    padding: 1px 8px;
    border-radius: 4px;
  }
}

.bg-date-transparent {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  width: auto;
  word-break: break-word;

  span {
    background-color: var(--color-primary-opacity);
    color: var(--text-color-primary);
    padding: 2px 8px;
    border-radius: var(--border-radius-xs);
    border: var(--normal-sec-border);
  }
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-30 {
  gap: 30px;
}

.gap-20 {
  gap: 20px;
}

.gap-1 {
  gap: 4px;
}

.gap-5 {
  gap: 5px;
}
.gap-10 {
  gap: 10px !important;
}

.table-border-wrap {
  box-shadow: none;
  .MuiDataGrid-root {
    border: 1px solid $color-Dark-10;
    border-radius: 8px;
    padding-bottom: 0px;
    margin-top: 20px;
    overflow: hidden;
    .MuiDataGrid-columnHeaders {
      .MuiDataGrid-row--borderBottom {
        background-color: $color-off-white;
      }
    }
  }
}

.table-list-tooltip {
  max-width: 800px !important;
  width: 100% !important;
  background-color: $color-White !important;
  color: $color-Dark-50 !important;
  border: 1px solid $color-Dark-50 !important;
  border-radius: 0px !important;
  max-height: 400px;
  overflow: auto;
  margin-top: 5px !important;
}

.sun-editor {
  width: 100% !important;
}

.custom-editor {
  .sun-editor {
    .se-container {
      .se-submenu {
        top: 10px !important;
      }
    }

    .se-wrapper {
      .se-wrapper-code {
        background-color: $color-secondary;
        color: $color-Black;
      }
    }
  }

  ul {
    list-style: inside;
  }

  // p,
  // li {
  //   ul {
  //     list-style: inherit !important;
  //   }
  // }

  // allows default ol to show up
  ol {
    list-style: inside;
    list-style-type: number;
  }

  // allows table borders to show up
  td {
    border: solid 1px;
  }

  .jodit-status-bar__item-right .jodit-status-bar-link {
    display: none;
  }

  .jodit-add-new-line {
    display: none;
  }
}
.jodit__preview-box {
  ul,
  ol {
    list-style: inside;
  }
}

.sun-editor-editable {
  overflow: auto;
}

.pie-charts {
  .ag-charts-proxy-legend-pagination {
    display: block !important;
  }

  // .ag-charts-series-area {
  //   max-width: 100%;
  // }
  .title {
    color: $color-Dark-50;
  }
  .piechart-no-data {
    height: 400px;

    .no-data {
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
}

.input-label {
  top: -5px;
  left: 1px;
  transform: none !important;
  color: $color-Dark-80 !important;
  font-family: $PrimaryFont !important;
  line-height: 18px !important;
  margin-bottom: 2px !important;
  font-weight: 600 !important;
  background-color: transparent !important;
  font-size: 12px !important;
  letter-spacing: -0.5px !important;
  text-transform: capitalize !important;
  margin-bottom: 0 !important;

  span {
    text-transform: capitalize !important;
  }
}
.primary-small-icon {
  line-height: 0px;
  padding: 3.5px;
  border: var(--field-border-primary);
  border-radius: var(--btn-icon-border-radius);
  background-color: var(--btn-color-primary);
  svg {
    width: var(--icon-size-sm) !important;
    height: var(--icon-size-sm) !important;
    path {
      stroke: var(--icon-color-white) !important;
      stroke-width: 2px;
    }
  }
  &:hover {
    border: var(--field-border-primary);
    background-color: var(--btn-color-secondary);
    svg {
      path {
        stroke: var(--icon-color-primary) !important;
        stroke-width: 2px;
      }
    }
  }
}
.primary-assign-emp-icon {
  height: 20px;
  width: 20px;
  border: 1px solid $color-green;
  background-color: $color-green;
  svg {
    width: 18px !important;
    height: 18px !important;
    path {
      stroke: $color-White !important;
      stroke-width: 2px;
    }
  }
  &:hover {
    border: 1px solid $color-green;
    background-color: $color-White;
    svg {
      path {
        stroke: $color-green !important;
        stroke-width: 2px;
      }
    }
  }
}
.secondary-small-icon {
  line-height: 0px;
  padding: 3.5px;
  height: 30px;
  width: 30px;
  border: 1px solid $color-primary;
  border-radius: 8px;
  background-color: $color-White;
  .svg-icon {
    fill: $color-primary;
  }
  &:hover {
    background-color: $color-primary;
    box-shadow:
      0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1);
    .svg-icon {
      fill: $color-White;
    }
  }
}
.secondary-small-icon-wrap {
  line-height: 0px;
  padding: 3.5px;
  height: 30px;
  width: 40px;
  border: 1px solid $color-primary;
  border-radius: 8px;
  background-color: $color-primary;
  .MuiSvgIcon-root {
    fill: $color-White;
  }
  &:hover {
    background-color: $color-White;
    box-shadow:
      0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1);
    .MuiSvgIcon-root {
      fill: $color-primary;
    }
  }
}
.secondary-assign-emp-icon {
  height: 20px;
  width: 20px;
  border: 1px solid $color-Alert;
  border-radius: 8px;
  background-color: $color-White;
  .svg-icon {
    width: 18px !important;
    height: 18px !important;
    fill: $color-Alert;
  }
  &:hover {
    background-color: $color-Alert;
    box-shadow:
      0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1);
    .svg-icon {
      fill: $color-White;
    }
  }
}
.small-buttons {
  .save-btn,
  .secondary-button {
    padding: 5px 20px !important;
    width: 100% !important;
    max-width: 80px;
  }
  @media (max-width: 399px) {
    button:first-child {
      margin-top: auto !important;
    }
  }
}
.select-box-wrap {
  width: 100%;
  max-width: 230px;
  .MuiSelect-select {
    font-size: 14px;
    padding: 2px 7px 3px 15px;
    margin-top: 0px !important;
  }

  fieldset {
    height: 30px !important;
    border-radius: 4px;
    margin-top: 2.5px !important;
  }

  .MuiSvgIcon-root {
    margin-top: 1px;
  }

  .placeholder {
    margin-top: 2px;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-weight: 300 !important;
  }
  @media (max-width: 575px) {
    max-width: 100%;
  }
}
.select-small-box {
  .MuiSelect-select {
    padding: 2px 30px 3px 15px !important;
    .list-item {
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
    }
  }
}
.select-branch-small {
  max-width: 200px !important;
  .MuiSelect-select {
    padding: 2px 30px 3px 15px !important;
    .list-item {
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
    }
  }
}
.branch-list-dropdown-small {
  .MuiList-root {
    width: 230px;
  }
  .MuiMenuItem-root {
    font-family: $PrimaryFont !important;
    font-size: 14px !important;
    line-height: 18px !important;
    letter-spacing: -0.5px !important;
  }
}
.select-dropdown-small {
  .MuiList-root {
    width: 230px;
  }
  .MuiMenuItem-root {
    font-family: $PrimaryFont !important;
    font-size: 14px !important;
    line-height: 18px !important;
    letter-spacing: -0.5px !important;
  }
}
.filter-apply-btn {
  min-width: 40px !important;
  padding: 3.5px 0px !important;
  line-height: 0px;
  height: 30px;
  width: 40px !important;
  border: 1px solid $color-primary;
  border-radius: 4px !important;
  background-color: $color-primary;
  .MuiSvgIcon-root {
    fill: $color-White;
  }
  &:hover {
    background-color: $color-White;
    box-shadow:
      0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1);
    .MuiSvgIcon-root {
      fill: $color-primary !important;
    }
  }
}
.filter-clear-btn {
  min-width: 40px !important;
  padding: 3.5px 0px !important;
  line-height: 0px;
  height: 30px;
  width: 40px !important;
  border: 1px solid $color-primary !important;
  border-radius: 4px !important;
  background-color: $color-White;
  .MuiSvgIcon-root {
    fill: $color-primary !important;
  }
  &:hover {
    background-color: $color-primary;
    box-shadow:
      0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1);
    .MuiSvgIcon-root {
      fill: $color-White !important;
    }
  }
}

.filter-tune-icon {
  border: 1px solid $color-Black;
  border-radius: 3px;
  width: 25px;
  height: 25px;
  cursor: pointer;
}

.no-data-found {
  text-align: center;
  .no-data-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-gray);
  }
}

.intergalactic-multiselect {
  display: flex !important;
  align-items: center;
  button {
    border: var(--field-border) !important;
    background: var(--field-background) !important;
  }
  .select-all-wrap {
    padding: 5px;
    color: var(--text-color-primary);
    font-size: 14px;
    cursor: pointer;
  }
  [class^='___SFilterTrigger'] {
    background-color: var(--field-background);
    border-color: var(--field-border);
    border-radius: 5px;
  }
  [class*='__display-placeholder'] {
    text-transform: capitalize;
    font-family: var(--font-family-primary);
  }
  .multiple-branch-wrap {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: var(--spacing-xs);
  }
  .multiple-category-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-xs);
    .categories-wrap {
      color: var(--text-slate-gray);
      font-family: var(--font-family-primary);
    }
  }

  [class*='neighborLocation_right'],
  [class*='SBaseTrigger']:not([class*='neighborLocation_left']) {
    width: 100%;
    // min-width: 232px;
    // max-width: 232px;
    // min-width: 250px;
    // max-width: 250px;
    border-radius: 5px;
    border-radius: var(--field-radius);
    // border: var(--normal-sec-border);
    box-shadow: none;
    transition: border-color 0.2s ease-in-out;
    background: var(--field-background);
    cursor: pointer;
    min-height: 36px;
    svg {
      width: var(--icon-size-xsm);
      height: var(--icon-size-xsm);
      path {
        fill: var(--color-primary);
      }
    }
    .branches-wrap {
      color: var(--field-placeholder);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      font-family: var(--font-family-primary);
    }
    .label-wrap {
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      font-family: var(--font-family-primary);
    }
    .selected-count {
      border: 1px solid var(--color-primary);
      padding: 0px 6px;
      border-radius: 6px;
      background-color: var(--color-primary);
      color: var(--text-color-white);
      font-family: var(--font-family-primary);
    }

    .selected-wrap {
      color: var(--text-color-black);
      // font-weight: 500;
      font-family: var(--font-family-primary);
    }

    .down-arrow-wrap {
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
      fill: var(--color-primary);
      transition: all 0.2s ease-in-out;
      // transform: scaleY(-1) !important;
    }

    &[aria-expanded='true'] {
      .___SIcon_3q66l_gg_ {
        transition: all 0.2s ease-in-out;
        transform: scaleY(-1);
      }
      .down-arrow-wrap {
        transform: scaleY(-1);
      }
    }

    .clear-icon-wrap {
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
      fill: var(--icon-color-slate-gray);
      path {
        fill: var(--icon-color-slate-gray);
      }
    }
    .___SInner_oiek1_gg_ {
      justify-content: flex-start;
      align-items: center;
      gap: 5px;

      .___SIcon_lu74z_gg_ {
        fill: var(--color-primary);
      }
    }
  }
  [class*='__disabled'] {
    opacity: 1 !important;
    cursor: not-allowed !important;
    pointer-events: inherit !important;
  }

  [class*='SDropdownItem'] {
    min-height: 18px;
    height: 18px;
  }

  [aria-label='Clear'] {
    display: none;
  }
}
.select-input-wrap {
  z-index: 99999 !important;
  animation-fill-mode: none !important;
  .select-all-wrap {
    padding: 5px;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
  }

  .apply-btn {
    background-color: var(--color-white) !important;
  }
}
